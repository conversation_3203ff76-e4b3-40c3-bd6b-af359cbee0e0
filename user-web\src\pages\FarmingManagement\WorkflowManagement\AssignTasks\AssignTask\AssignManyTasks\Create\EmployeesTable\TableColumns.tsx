import {
  CheckCircleOutlined,
  CopyOutlined,
  DeleteOutlined,
  PlusOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Button, Input, InputNumber, Select, Tag, Tooltip } from 'antd';
import React from 'react';

import { AllocationState, EnvironmentState, TaskAssignmentColumnType, TaskState } from './types';
import { getTagColor } from './utils';

interface TableColumnsProps {
  environmentState: EnvironmentState;
  taskState: TaskState;
  allocationState: AllocationState;
  availableEnvironmentIds: string[];
  setTaskAssignmentOfSpecificEmployee: React.Dispatch<
    React.SetStateAction<readonly { [key: string]: any }[]>
  >;
  handleAutoFill: (row: any, index: number) => void;
  handleRemoveADataRow: (idx: number) => void;
  handleAddMoreTaskOnDay: (row: any, index: number) => void;
}

export const useTableColumns = ({
  environmentState,
  taskState,
  allocationState,
  availableEnvironmentIds,
  setTaskAssignmentOfSpecificEmployee,
  handleAutoFill,
  handleRemoveADataRow,
  handleAddMoreTaskOnDay,
}: TableColumnsProps): ProColumns<TaskAssignmentColumnType>[] => {
  const { formatMessage } = useIntl();
  const { listOfAllEnvironmentTemplates } = environmentState;
  const { listOfTaskTemplates } = taskState;
  const { validateAllocation } = allocationState;

  const columns: ProColumns<TaskAssignmentColumnType>[] = [
    {
      width: 50,
      dataIndex: 'autoFill',
      render: (_, row, index) => (
        <div className="flex flex-row justify-center gap-x-2">
          <Tooltip title={formatMessage({ id: 'common.delete' })}>
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleRemoveADataRow(index)}
              className="opacity-50 hover:opacity-100"
            />
          </Tooltip>
          <Tooltip title={formatMessage({ id: 'common.clone' })}>
            <Button
              icon={<CopyOutlined />}
              onClick={() => handleAutoFill(row, index)}
              className="opacity-40 hover:opacity-100"
            />
          </Tooltip>
          <Tooltip title={formatMessage({ id: 'common.add_more_task_on_day' })}>
            <Button
              icon={<PlusOutlined />}
              onClick={() => handleAddMoreTaskOnDay(row, index)}
              className="opacity-40 hover:opacity-100"
            />
          </Tooltip>
        </div>
      ),
    },
    {
      width: 20,
      title: 'STT',
      dataIndex: 'stt',
      valueType: 'index',
      render: (_, record, index) => index + 1,
      hideInTable: true,
      hideInSearch: true,
    },
    {
      width: 200,
      title: formatMessage({
        id: 'workflowTab.assignTasks.employees',
      }),
      dataIndex: 'assigned_to_employee_fullName',
      valueType: 'text',
    },
    {
      width: 150,
      title: formatMessage({ id: 'workflowTab.assignTasks.time' }),
      dataIndex: 'time',
      valueType: 'text',
    },
    {
      width: 280,
      title: formatMessage({ id: 'workflowTab.assignTasks.task' }),
      dataIndex: 'task',
      valueType: 'select',
      fieldProps: {
        options: listOfTaskTemplates,
      },
      render(dom, entity) {
        return (
          <Select
            showSearch
            style={{ width: '250px' }}
            value={entity.task}
            disabled={availableEnvironmentIds.length === 0}
            placeholder={
              availableEnvironmentIds.length === 0 ? 'Chọn môi trường trước' : 'Chọn công việc'
            }
            filterOption={(input, option) =>
              (option?.label?.toString().toLowerCase() ?? '').includes(input.toLowerCase())
            }
            allowClear
            optionFilterProp="label"
            onChange={(value) => {
              setTaskAssignmentOfSpecificEmployee((prev) => {
                const index = prev.findIndex((item, index) => index + 1 === entity.stt);
                if (index === -1) return prev;
                const newData = [...prev];
                const task = listOfTaskTemplates.find((item) => item.value === value);
                console.log('task selected: ', task);
                newData[index] = {
                  ...newData[index],
                  task: value,
                  status: 'Plan',
                  priority_level: task?.priority_level,
                  environment_label: listOfAllEnvironmentTemplates.find(
                    (item) => item.name === task?.environment_template_id,
                  )?.label,
                  // environment_label: task?.environment_label,
                  task_type: task?.task_type,
                  item_task:
                    task?.item_task
                      ?.map((item_task: any) => `${item_task.item.item_name || 'N/A'}`)
                      .join(', ') || '',
                  production_task:
                    task?.production_task
                      ?.map((prod_task: any) => `${prod_task.item.item_name || 'N/A'}`)
                      .join(', ') || '',
                };
                return newData;
              });
            }}
            options={listOfTaskTemplates}
          />
        );
      },
    },
    {
      width: 200,
      title: formatMessage({ id: 'common.form-management.status' }),
      dataIndex: 'status',
      render(dom, entity) {
        return (
          <Select
            style={{ width: '180px' }}
            value={entity.status}
            defaultValue={'Plan'}
            onChange={(value) => {
              setTaskAssignmentOfSpecificEmployee((prev) => {
                const index = prev.findIndex((item, index) => index + 1 === entity.stt);
                if (index === -1) return prev;
                const newData = [...prev];
                newData[index] = {
                  ...newData[index],
                  status: value,
                };
                return newData;
              });
            }}
            options={[
              {
                label: (
                  <Tag color={getTagColor('Done').color} className="w-full">
                    {getTagColor('Done').status}
                  </Tag>
                ),
                value: 'Done',
              },
              {
                label: (
                  <Tag color={getTagColor('In progress').color} className="w-full">
                    {getTagColor('In progress').status}
                  </Tag>
                ),
                value: 'In progress',
              },
              {
                label: (
                  <Tag color={getTagColor('Pending').color} className="w-full">
                    {getTagColor('Pending').status}
                  </Tag>
                ),
                value: 'Pending',
              },
              {
                label: (
                  <Tag color={getTagColor('Plan').color} className="w-full">
                    {getTagColor('Plan').status}
                  </Tag>
                ),
                value: 'Plan',
              },
            ]}
          />
        );
      },
    },
    {
      width: 180,
      title: formatMessage({ id: 'common.priority_level' }),
      dataIndex: 'priority_level',
      render(dom, entity) {
        return (
          <Tag color={getTagColor(entity.priority_level).color}>
            {getTagColor(entity.priority_level).status}
          </Tag>
        );
      },
    },
    {
      width: 150,
      title: formatMessage({ id: 'workflowTab.environment' }),
      dataIndex: 'environment_label',
      valueType: 'text',
      hideInSearch: true,
      render(dom, entity) {
        return <p className="text-gray-500">{entity.environment_label}</p>;
      },
    },
    {
      width: 180,
      title: formatMessage({ id: 'workflowTab.assignTasks.exp_quantity' }),
      dataIndex: 'exp_quantity',
      valueType: 'digit',
      render(dom, entity) {
        // console.log("entity", entity.allocationMessage)
        return (
          <div>
            <InputNumber
              style={{ minWidth: '100%' }}
              value={entity.exp_quantity}
              // status={entity.allocationValid === false && entity.exp_quantity ? 'error' : undefined}
              onChange={(value) => {
                // Update the task's quantity
                setTaskAssignmentOfSpecificEmployee((prev) => {
                  const index = prev.findIndex((item, index) => index + 1 === entity.stt);
                  if (index === -1) return prev;
                  const newData = [...prev];
                  newData[index] = { ...newData[index], exp_quantity: value };
                  return newData;
                });

                // If we have all the required information, validate allocation
                if (value && entity.production_task && entity.time) {
                  console.log('[TableColumns] Validating allocation for task', {
                    stt: entity.stt,
                    production_task: entity.production_task,
                    time: entity.time,
                    quantity: value,
                  });
                  validateAllocation(
                    entity.stt,
                    entity.production_task,
                    entity.time,
                    value as number,
                  );
                }
              }}
            />
            {entity.allocationMessage && (
              <div style={{ marginTop: '4px' }}>
                <Tooltip title={entity.allocationMessage}>
                  {entity.allocationValid ? (
                    <Tag icon={<CheckCircleOutlined />} color="success">
                      {entity.allocationMessage.length > 12
                        ? entity.allocationMessage.substring(0, 12) + '...'
                        : entity.allocationMessage}
                    </Tag>
                  ) : (
                    <Tag icon={<WarningOutlined />} color="error">
                      {entity.allocationMessage.length > 12
                        ? entity.allocationMessage.substring(0, 12) + '...'
                        : entity.allocationMessage}
                    </Tag>
                  )}
                </Tooltip>
              </div>
            )}
          </div>
        );
      },
      hideInSearch: true,
    },
    {
      width: 150,
      title: formatMessage({ id: 'workflowTab.assignTasks.product' }),
      dataIndex: 'production_task',
      valueType: 'text',
      hideInSearch: true,
      render(dom, entity) {
        console.log('entity is: ', entity);
        // If production_task is already formatted, split and display
        const productionTaskArray = entity.production_task?.split(', ') || [];
        console.log('productionTaskArray: ', productionTaskArray);
        return (
          <div className="flex flex-col items-start gap-1">
            {productionTaskArray.map((item, index) => (
              <Tag key={index} className="w-full text-wrap">
                {item}
              </Tag>
            ))}
          </div>
        );
      },
    },
    {
      width: 200,
      title: formatMessage({ id: 'workflowTab.assignTasks.itemTask' }),
      dataIndex: 'item_task',
      valueType: 'text',
      render(dom, entity) {
        // If item_task is already formatted, split and display
        const itemTaskArray = entity.item_task?.split(', ') || [];
        return (
          <div className="flex flex-col items-start gap-1">
            {itemTaskArray.map((item, index) => (
              <Tag key={index} className="w-full text-wrap">
                {item}
              </Tag>
            ))}
          </div>
        );
      },
      hideInSearch: true,
    },
    {
      title: formatMessage({ id: 'common.description' }),
      dataIndex: 'description',
      valueType: 'text',
      hideInSearch: true,
      render(_, entity) {
        return (
          <Input.TextArea
            rows={1}
            value={entity.description}
            onChange={(e) => {
              setTaskAssignmentOfSpecificEmployee((prev) => {
                const index = prev.findIndex((item, index) => index + 1 === entity.stt);
                if (index === -1) return prev;
                const newData = [...prev];
                newData[index] = { ...newData[index], description: e.target.value };
                return newData;
              });
            }}
          />
        );
      },
      width: 200,
    },
  ];

  return columns;
};
